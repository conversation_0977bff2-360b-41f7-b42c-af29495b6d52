#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证优化是否正确应用
"""

import pandas as pd
import os
import time

def test_single_file_processing():
    """测试单个文件的处理性能"""
    print("验证优化效果 - 单文件处理测试")
    print("=" * 40)
    
    data_file = "processed_company_data_20250729_160206/batch_001.parquet"
    if not os.path.exists(data_file):
        print(f"测试文件不存在: {data_file}")
        return
    
    print(f"测试文件: {data_file}")
    
    # 读取数据
    start_time = time.time()
    df = pd.read_parquet(data_file, columns=['lc_company_id', 'industry_l1_code'])
    read_time = time.time() - start_time
    
    print(f"读取数据: {len(df):,} 条记录, 用时 {read_time:.3f}秒")
    
    # 过滤空值
    start_time = time.time()
    df = df.dropna(subset=['industry_l1_code'])
    filter_time = time.time() - start_time
    
    print(f"过滤空值: {len(df):,} 条有效记录, 用时 {filter_time:.3f}秒")
    
    # 使用优化后的方法创建映射表
    start_time = time.time()
    industry_mapping = dict(zip(df['lc_company_id'], df['industry_l1_code']))
    mapping_time = time.time() - start_time
    
    print(f"创建映射表: {len(industry_mapping):,} 个企业, 用时 {mapping_time:.3f}秒")
    
    total_time = read_time + filter_time + mapping_time
    print(f"总用时: {total_time:.3f}秒")
    
    # 验证映射表内容
    print(f"\n映射表样本 (前5个):")
    sample_items = list(industry_mapping.items())[:5]
    for company_id, industry_code in sample_items:
        print(f"  {company_id}: {industry_code}")
    
    return industry_mapping

def test_industry_classification(industry_mapping):
    """测试行业分类逻辑"""
    print("\n" + "=" * 40)
    print("验证行业分类逻辑")
    print("=" * 40)
    
    # 行业分类定义
    secondary = {'B', 'C', 'D', 'E'}
    tertiary = {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}
    
    # 统计各行业企业数量
    industry_counts = {}
    secondary_count = 0
    tertiary_count = 0
    other_count = 0
    
    for company_id, industry_code in industry_mapping.items():
        if industry_code not in industry_counts:
            industry_counts[industry_code] = 0
        industry_counts[industry_code] += 1
        
        if industry_code in secondary:
            secondary_count += 1
        elif industry_code in tertiary:
            tertiary_count += 1
        else:
            other_count += 1
    
    total = len(industry_mapping)
    
    print(f"总企业数: {total:,}")
    print(f"第二产业: {secondary_count:,} ({secondary_count/total*100:.2f}%)")
    print(f"第三产业: {tertiary_count:,} ({tertiary_count/total*100:.2f}%)")
    print(f"其他行业: {other_count:,} ({other_count/total*100:.2f}%)")
    
    print(f"\n各行业分布:")
    for industry_code in sorted(industry_counts.keys()):
        count = industry_counts[industry_code]
        category = "第二产业" if industry_code in secondary else "第三产业" if industry_code in tertiary else "其他"
        print(f"  {industry_code}: {count:,} ({category})")

def test_year_file_sample():
    """测试年份文件读取"""
    print("\n" + "=" * 40)
    print("验证年份文件读取")
    print("=" * 40)
    
    analysis_dir = "company_status_analysis_20250729_163829"
    if not os.path.exists(analysis_dir):
        print(f"分析目录不存在: {analysis_dir}")
        return
    
    # 测试读取2017年文件
    test_file = os.path.join(analysis_dir, "normal_companies_2017.txt")
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print(f"测试文件: {test_file}")
    
    start_time = time.time()
    with open(test_file, 'r', encoding='utf-8') as f:
        company_ids = [line.strip() for line in f if line.strip()]
    read_time = time.time() - start_time
    
    print(f"读取企业ID: {len(company_ids):,} 个, 用时 {read_time:.3f}秒")
    
    # 显示样本
    print(f"企业ID样本 (前5个):")
    for i, company_id in enumerate(company_ids[:5], 1):
        print(f"  {i}. {company_id}")
    
    return company_ids

def simulate_analysis(industry_mapping, company_ids):
    """模拟分析过程"""
    print("\n" + "=" * 40)
    print("模拟行业分布分析")
    print("=" * 40)
    
    # 取前10000个企业进行快速测试
    test_company_ids = company_ids[:10000]
    print(f"测试企业数: {len(test_company_ids):,}")
    
    # 行业分类
    secondary = {'B', 'C', 'D', 'E'}
    tertiary = {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}
    
    start_time = time.time()
    
    secondary_count = 0
    tertiary_count = 0
    unknown_count = 0
    
    for company_id in test_company_ids:
        industry_code = industry_mapping.get(company_id)
        
        if industry_code is None:
            unknown_count += 1
        elif industry_code in secondary:
            secondary_count += 1
        elif industry_code in tertiary:
            tertiary_count += 1
        else:
            unknown_count += 1
    
    analysis_time = time.time() - start_time
    
    total = len(test_company_ids)
    secondary_ratio = (secondary_count / total * 100) if total > 0 else 0
    tertiary_ratio = (tertiary_count / total * 100) if total > 0 else 0
    unknown_ratio = (unknown_count / total * 100) if total > 0 else 0
    
    print(f"分析用时: {analysis_time:.3f}秒")
    print(f"结果:")
    print(f"  总企业数: {total:,}")
    print(f"  第二产业: {secondary_count:,} ({secondary_ratio:.2f}%)")
    print(f"  第三产业: {tertiary_count:,} ({tertiary_ratio:.2f}%)")
    print(f"  未知行业: {unknown_count:,} ({unknown_ratio:.2f}%)")

def main():
    """主函数"""
    print("🔍 验证企业行业分析优化效果")
    print("=" * 50)
    
    # 1. 测试单文件处理
    industry_mapping = test_single_file_processing()
    if not industry_mapping:
        return
    
    # 2. 验证行业分类逻辑
    test_industry_classification(industry_mapping)
    
    # 3. 测试年份文件读取
    company_ids = test_year_file_sample()
    if not company_ids:
        return
    
    # 4. 模拟分析过程
    simulate_analysis(industry_mapping, company_ids)
    
    print("\n" + "=" * 50)
    print("✅ 验证完成！优化效果良好")
    print("📊 建议运行完整的 industry_analysis_lite.py 进行全量分析")

if __name__ == "__main__":
    main()
