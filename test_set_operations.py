#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集合操作逻辑的正确性
验证修复后的企业状态计算公式
"""

def test_set_operations():
    """测试集合操作的正确性"""
    print("测试集合操作逻辑...")
    
    # 模拟数据
    current_normal = {'A', 'B', 'C', 'D', 'E'}  # 下一年正常企业
    established = {'F', 'G'}                     # 下一年新成立企业
    cancelled = {'B'}                            # 下一年注销企业
    revoked = {'C'}                              # 下一年吊销企业
    
    print(f"下一年正常企业: {sorted(current_normal)}")
    print(f"下一年新成立企业: {sorted(established)}")
    print(f"下一年注销企业: {sorted(cancelled)}")
    print(f"下一年吊销企业: {sorted(revoked)}")
    
    # 原始错误的操作（会报错）
    try:
        wrong_result = current_normal - established + cancelled + revoked
        print("错误操作成功（不应该发生）")
    except TypeError as e:
        print(f"✅ 预期的TypeError: {e}")
    
    # 修复后的正确操作
    correct_result = current_normal - established | cancelled | revoked
    print(f"修复后结果: {sorted(correct_result)}")
    
    # 验证逻辑正确性
    print("\n逻辑验证:")
    print("某年正常企业 = 下一年正常企业 - 下一年新成立 + 下一年注销 + 下一年吊销")
    
    # 分步计算
    step1 = current_normal - established  # 移除新成立的
    print(f"1. 移除新成立企业: {sorted(current_normal)} - {sorted(established)} = {sorted(step1)}")
    
    step2 = step1 | cancelled  # 加回注销的
    print(f"2. 加回注销企业: {sorted(step1)} ∪ {sorted(cancelled)} = {sorted(step2)}")
    
    step3 = step2 | revoked  # 加回吊销的
    print(f"3. 加回吊销企业: {sorted(step2)} ∪ {sorted(revoked)} = {sorted(step3)}")
    
    print(f"\n最终结果: {sorted(step3)}")
    print(f"一步计算结果: {sorted(correct_result)}")
    print(f"结果一致: {step3 == correct_result}")
    
    # 业务逻辑解释
    print("\n业务逻辑解释:")
    print("- A, D, E: 在下一年正常，且没有在下一年成立/注销/吊销，所以在当年也正常")
    print("- B: 在下一年正常，但在下一年被注销，说明在当年是正常的")
    print("- C: 在下一年正常，但在下一年被吊销，说明在当年是正常的")
    print("- F, G: 在下一年新成立，所以当年不存在")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "="*50)
    print("测试边界情况...")
    
    # 空集合情况
    current_normal = {'A', 'B', 'C'}
    established = set()  # 空集合
    cancelled = set()    # 空集合
    revoked = set()      # 空集合
    
    result = current_normal - established | cancelled | revoked
    print(f"空集合测试: {sorted(current_normal)} -> {sorted(result)}")
    print(f"结果正确: {result == current_normal}")
    
    # 重叠情况
    current_normal = {'A', 'B', 'C', 'D'}
    established = {'E'}
    cancelled = {'A'}    # A既在正常企业中，又在注销企业中
    revoked = {'B'}      # B既在正常企业中，又在吊销企业中
    
    result = current_normal - established | cancelled | revoked
    expected = {'A', 'B', 'C', 'D'}  # A和B会被重新加回来
    print(f"重叠测试: {sorted(result)}")
    print(f"预期结果: {sorted(expected)}")
    print(f"结果正确: {result == expected}")

if __name__ == "__main__":
    test_set_operations()
    test_edge_cases()
    print("\n✅ 集合操作测试完成！")
