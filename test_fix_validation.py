#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证修复后的企业状态分析逻辑
使用少量数据测试集合操作是否正常
"""

import pandas as pd
import os
from datetime import datetime

def test_with_sample_data():
    """使用样本数据测试修复后的逻辑"""
    print("开始测试修复后的企业状态分析逻辑...")
    
    # 检查是否有处理后的数据
    data_dir = "processed_company_data_20250729_160206"
    if not os.path.exists(data_dir):
        print(f"数据目录不存在: {data_dir}")
        return
    
    # 只读取第一个文件进行测试
    first_file = os.path.join(data_dir, "batch_001.parquet")
    if not os.path.exists(first_file):
        print(f"测试文件不存在: {first_file}")
        return
    
    print(f"读取测试文件: {first_file}")
    
    try:
        df = pd.read_parquet(first_file)
        print(f"原始数据: {len(df):,} 条记录")
        
        # 过滤有效记录
        date_columns = ['establish_date', 'cancel_date', 'revoke_date']
        valid_mask = ~(df[date_columns].isna().all(axis=1))
        df = df[valid_mask].copy()
        print(f"有效数据: {len(df):,} 条记录")
        
        if len(df) == 0:
            print("没有有效数据，跳过测试")
            return
        
        # 提取2024年正常企业
        normal_2024 = set(df[df['company_status_clean'] == 1]['lc_company_id'].unique())
        print(f"2024年正常企业: {len(normal_2024):,} 个")
        
        # 提取年份
        df['establish_year'] = df['establish_date'].dt.year
        df['cancel_year'] = df['cancel_date'].dt.year
        df['revoke_year'] = df['revoke_date'].dt.year
        
        # 测试2023年计算
        print("\n测试2023年企业状态计算...")
        
        # 获取2024年的事件
        established_2024 = set(df[df['establish_year'] == 2024]['lc_company_id'].unique())
        cancelled_2024 = set(df[df['cancel_year'] == 2024]['lc_company_id'].unique())
        revoked_2024 = set(df[df['revoke_year'] == 2024]['lc_company_id'].unique())
        
        print(f"2024年新成立: {len(established_2024):,} 个")
        print(f"2024年注销: {len(cancelled_2024):,} 个")
        print(f"2024年吊销: {len(revoked_2024):,} 个")
        
        # 使用修复后的集合操作
        print("\n应用修复后的集合操作...")
        normal_2023 = normal_2024 - established_2024 | cancelled_2024 | revoked_2024
        
        print(f"✅ 集合操作成功！")
        print(f"2023年正常企业: {len(normal_2023):,} 个")
        print(f"变化: {len(normal_2023) - len(normal_2024):+,} 个")
        
        # 验证逻辑合理性
        print("\n逻辑验证:")
        step1 = normal_2024 - established_2024
        print(f"1. 移除2024年新成立: {len(normal_2024):,} - {len(established_2024):,} = {len(step1):,}")
        
        step2 = step1 | cancelled_2024
        print(f"2. 加回2024年注销: {len(step1):,} + {len(cancelled_2024):,} = {len(step2):,}")
        
        step3 = step2 | revoked_2024
        print(f"3. 加回2024年吊销: {len(step2):,} + {len(revoked_2024):,} = {len(step3):,}")
        
        print(f"最终结果一致: {len(step3) == len(normal_2023)}")
        
        # 测试更多年份
        print("\n测试更多年份...")
        current_normal = normal_2024.copy()
        
        for year in range(2023, 2020, -1):  # 测试2023-2021年
            next_year = year + 1
            
            established = set(df[df['establish_year'] == next_year]['lc_company_id'].unique())
            cancelled = set(df[df['cancel_year'] == next_year]['lc_company_id'].unique())
            revoked = set(df[df['revoke_year'] == next_year]['lc_company_id'].unique())
            
            # 使用修复后的操作
            current_normal = current_normal - established | cancelled | revoked
            
            print(f"{year}年正常企业: {len(current_normal):,} 个")
        
        print("\n✅ 所有测试通过！修复成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_with_sample_data()
