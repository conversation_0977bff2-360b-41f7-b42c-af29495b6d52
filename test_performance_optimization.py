#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化测试脚本
比较不同方法创建企业行业映射表的性能
"""

import pandas as pd
import time
import os

def method_1_iterrows(df):
    """方法1: 使用iterrows()（低效方法）"""
    mapping = {}
    for _, row in df.iterrows():
        mapping[row['lc_company_id']] = row['industry_l1_code']
    return mapping

def method_2_zip(df):
    """方法2: 使用dict(zip())（高效方法）"""
    return dict(zip(df['lc_company_id'], df['industry_l1_code']))

def method_3_set_index(df):
    """方法3: 使用set_index().to_dict()（另一种高效方法）"""
    return df.set_index('lc_company_id')['industry_l1_code'].to_dict()

def method_4_values(df):
    """方法4: 使用values数组（最高效方法）"""
    return dict(zip(df['lc_company_id'].values, df['industry_l1_code'].values))

def test_performance():
    """测试各种方法的性能"""
    print("企业行业映射表创建性能测试")
    print("=" * 50)
    
    # 检查测试数据
    data_file = "processed_company_data_20250729_160206/batch_001.parquet"
    if not os.path.exists(data_file):
        print(f"测试数据文件不存在: {data_file}")
        return
    
    # 读取测试数据
    print("读取测试数据...")
    df = pd.read_parquet(data_file, columns=['lc_company_id', 'industry_l1_code'])
    df = df.dropna(subset=['industry_l1_code'])  # 过滤空值
    
    # 测试不同数据量
    test_sizes = [1000, 10000, 100000, len(df)]
    
    methods = [
        ("iterrows()", method_1_iterrows),
        ("dict(zip())", method_2_zip),
        ("set_index().to_dict()", method_3_set_index),
        ("dict(zip(values))", method_4_values)
    ]
    
    print(f"原始数据大小: {len(df):,} 条记录")
    print()
    
    for size in test_sizes:
        if size > len(df):
            continue
            
        print(f"测试数据量: {size:,} 条记录")
        print("-" * 30)
        
        # 准备测试数据
        test_df = df.head(size).copy()
        
        results = {}
        
        for method_name, method_func in methods:
            # 跳过大数据量的iterrows测试（太慢了）
            if size > 10000 and method_name == "iterrows()":
                print(f"{method_name:20}: 跳过（数据量太大）")
                continue
            
            # 执行性能测试
            start_time = time.time()
            
            try:
                mapping = method_func(test_df)
                end_time = time.time()
                
                elapsed_time = end_time - start_time
                results[method_name] = elapsed_time
                
                # 验证结果正确性
                expected_size = len(test_df)
                actual_size = len(mapping)
                
                print(f"{method_name:20}: {elapsed_time:.4f}秒, 映射表大小: {actual_size:,}")
                
                if actual_size != expected_size:
                    print(f"  ⚠️ 警告: 映射表大小不匹配 (期望: {expected_size:,})")
                
            except Exception as e:
                print(f"{method_name:20}: 错误 - {str(e)}")
        
        # 计算性能提升
        if len(results) > 1:
            print("\n性能对比:")
            baseline_method = "iterrows()" if "iterrows()" in results else list(results.keys())[0]
            baseline_time = results[baseline_method]
            
            for method_name, elapsed_time in results.items():
                if method_name != baseline_method:
                    if elapsed_time > 0:
                        speedup = baseline_time / elapsed_time
                        print(f"  {method_name} 比 {baseline_method} 快 {speedup:.1f}x")
                    else:
                        print(f"  {method_name} 执行时间太短，无法准确测量")
        
        print()

def test_memory_usage():
    """测试内存使用情况"""
    print("内存使用测试")
    print("=" * 30)
    
    data_file = "processed_company_data_20250729_160206/batch_001.parquet"
    if not os.path.exists(data_file):
        print(f"测试数据文件不存在: {data_file}")
        return
    
    # 读取测试数据
    df = pd.read_parquet(data_file, columns=['lc_company_id', 'industry_l1_code'])
    df = df.dropna(subset=['industry_l1_code'])
    test_df = df.head(100000).copy()  # 使用10万条记录测试
    
    print(f"测试数据: {len(test_df):,} 条记录")
    
    try:
        import psutil

        process = psutil.Process(os.getpid())
        
        # 测试优化后的方法
        print("\n测试优化后的方法 (dict(zip())):")
        mem_before = process.memory_info().rss / 1024 / 1024  # MB
        
        start_time = time.time()
        mapping = dict(zip(test_df['lc_company_id'], test_df['industry_l1_code']))
        end_time = time.time()
        
        mem_after = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"  执行时间: {end_time - start_time:.4f}秒")
        print(f"  内存使用: {mem_before:.1f}MB -> {mem_after:.1f}MB (增加: {mem_after - mem_before:.1f}MB)")
        print(f"  映射表大小: {len(mapping):,}")
        
    except ImportError:
        print("需要安装psutil库来测试内存使用: pip install psutil")

def demonstrate_real_world_impact():
    """演示真实场景下的性能影响"""
    print("真实场景性能影响演示")
    print("=" * 40)
    
    data_file = "processed_company_data_20250729_160206/batch_001.parquet"
    if not os.path.exists(data_file):
        print(f"测试数据文件不存在: {data_file}")
        return
    
    # 模拟处理多个文件
    num_files = 108  # 实际文件数量
    
    df = pd.read_parquet(data_file, columns=['lc_company_id', 'industry_l1_code'])
    df = df.dropna(subset=['industry_l1_code'])
    test_df = df.head(50000).copy()  # 每个文件5万条记录
    
    print(f"模拟场景: {num_files} 个文件, 每个文件 {len(test_df):,} 条记录")
    print(f"总记录数: {num_files * len(test_df):,} 条")
    
    # 测试优化后的方法
    print("\n使用优化后的方法 (dict(zip())):")
    start_time = time.time()
    
    total_mapping = {}
    for i in range(min(5, num_files)):  # 只测试前5个文件
        batch_mapping = dict(zip(test_df['lc_company_id'], test_df['industry_l1_code']))
        total_mapping.update(batch_mapping)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print(f"  处理 5 个文件用时: {elapsed_time:.4f}秒")
    print(f"  平均每个文件: {elapsed_time/5:.4f}秒")
    print(f"  预估处理全部 {num_files} 个文件需要: {elapsed_time/5*num_files:.1f}秒 ({elapsed_time/5*num_files/60:.1f}分钟)")
    
    # 对比iterrows方法的预估时间
    print("\n如果使用iterrows()方法的预估时间:")
    # 基于小样本测试，iterrows大约慢50-100倍
    estimated_iterrows_time = elapsed_time * 75  # 假设慢75倍
    print(f"  预估处理全部文件需要: {estimated_iterrows_time/5*num_files:.1f}秒 ({estimated_iterrows_time/5*num_files/60:.1f}分钟)")
    print(f"  性能提升: 约 {estimated_iterrows_time/elapsed_time:.0f}x")

if __name__ == "__main__":
    # 运行性能测试
    test_performance()
    
    # 测试内存使用
    test_memory_usage()
    
    # 演示真实场景影响
    demonstrate_real_world_impact()
