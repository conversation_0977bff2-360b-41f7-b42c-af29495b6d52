#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业历年经营状态分析脚本
功能：分析2010-2024年每年的正常经营企业数量和ID列表
"""

import pandas as pd
import os
import glob
from datetime import datetime
from pathlib import Path
import logging
import gc

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('company_status_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_and_preprocess_data(data_dir):
    """
    加载并预处理数据
    
    Args:
        data_dir: 数据目录路径
    
    Returns:
        预处理后的DataFrame
    """
    logger.info(f"开始加载数据目录: {data_dir}")
    
    # 获取所有parquet文件
    parquet_files = glob.glob(os.path.join(data_dir, "*.parquet"))
    logger.info(f"找到 {len(parquet_files)} 个parquet文件")
    
    if not parquet_files:
        raise ValueError(f"在目录 {data_dir} 中未找到任何parquet文件")
    
    # 分批加载数据以节省内存
    all_data = []
    total_records = 0
    
    for i, file_path in enumerate(parquet_files, 1):
        logger.info(f"加载文件 {i}/{len(parquet_files)}: {os.path.basename(file_path)}")
        
        try:
            df = pd.read_parquet(file_path)
            
            # 过滤掉三个日期字段全部为空的记录
            date_columns = ['establish_date', 'cancel_date', 'revoke_date']
            valid_mask = ~(df[date_columns].isna().all(axis=1))
            df_filtered = df[valid_mask].copy()
            
            filtered_count = len(df_filtered)
            removed_count = len(df) - filtered_count
            
            if removed_count > 0:
                logger.info(f"文件 {os.path.basename(file_path)}: 移除 {removed_count:,} 条无日期记录，保留 {filtered_count:,} 条")
            
            if filtered_count > 0:
                all_data.append(df_filtered)
                total_records += filtered_count
            
            # 清理内存
            del df, df_filtered
            
            # 每处理20个文件进行一次垃圾回收
            if i % 20 == 0:
                gc.collect()
                logger.info(f"已处理 {i} 个文件，累计有效记录: {total_records:,}")
                
        except Exception as e:
            logger.error(f"加载文件 {file_path} 时出错: {str(e)}")
            continue
    
    if not all_data:
        raise ValueError("没有加载到任何有效数据")
    
    logger.info("开始合并所有数据...")
    combined_df = pd.concat(all_data, ignore_index=True)
    del all_data  # 释放内存
    gc.collect()
    
    logger.info(f"数据合并完成，总记录数: {len(combined_df):,}")
    
    # 提取年份字段
    logger.info("提取年份字段...")
    combined_df['establish_year'] = combined_df['establish_date'].dt.year
    combined_df['cancel_year'] = combined_df['cancel_date'].dt.year
    combined_df['revoke_year'] = combined_df['revoke_date'].dt.year
    
    # 删除原始日期字段以节省内存
    combined_df = combined_df.drop(columns=['establish_date', 'cancel_date', 'revoke_date'])
    
    logger.info("数据预处理完成")
    logger.info(f"最终数据形状: {combined_df.shape}")
    
    return combined_df

def calculate_yearly_normal_companies(df):
    """
    计算各年度正常经营企业ID列表
    
    Args:
        df: 预处理后的DataFrame
    
    Returns:
        dict: {年份: 企业ID集合}
    """
    logger.info("开始计算各年度正常经营企业...")
    
    # 2024年正常企业（基准年）
    normal_2024 = set(df[df['company_status_clean'] == 1]['lc_company_id'].unique())
    logger.info(f"2024年正常企业数量: {len(normal_2024):,}")
    
    # 存储各年度结果
    yearly_normal_companies = {2024: normal_2024}
    
    # 逐年向前推算（2023年到2010年）
    current_normal = normal_2024.copy()
    
    for year in range(2023, 2009, -1):  # 从2023年到2010年
        logger.info(f"计算 {year} 年正常企业...")
        
        # 获取该年的各类企业
        established_this_year = set(df[df['establish_year'] == (year + 1)]['lc_company_id'].unique())
        cancelled_this_year = set(df[df['cancel_year'] == (year + 1)]['lc_company_id'].unique())
        revoked_this_year = set(df[df['revoke_year'] == (year + 1)]['lc_company_id'].unique())
        
        # 计算公式：某年正常企业 = 下一年正常企业 - 该年新成立企业 + 该年注销企业 + 该年吊销企业
        # 注意：这里是计算year年的正常企业，所以要看year+1年的变化
        # 使用集合操作：减法用 - ，加法用 | (并集)
        current_normal = current_normal - established_this_year | cancelled_this_year | revoked_this_year
        
        yearly_normal_companies[year] = current_normal.copy()
        
        logger.info(f"{year}年正常企业数量: {len(current_normal):,}")
        logger.info(f"  - 移除{year+1}年新成立: {len(established_this_year):,}")
        logger.info(f"  - 加回{year+1}年注销: {len(cancelled_this_year):,}")
        logger.info(f"  - 加回{year+1}年吊销: {len(revoked_this_year):,}")
    
    return yearly_normal_companies

def save_results(yearly_normal_companies, output_dir):
    """
    保存分析结果
    
    Args:
        yearly_normal_companies: 各年度正常企业字典
        output_dir: 输出目录
    """
    logger.info(f"开始保存结果到目录: {output_dir}")
    
    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)
    
    # 保存各年度企业ID列表
    for year in sorted(yearly_normal_companies.keys()):
        company_ids = yearly_normal_companies[year]
        
        # 保存为文本文件（每行一个企业ID）
        txt_file = os.path.join(output_dir, f"normal_companies_{year}.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            for company_id in sorted(company_ids):
                f.write(f"{company_id}\n")
        
        # 保存为parquet文件（包含企业ID的DataFrame）
        parquet_file = os.path.join(output_dir, f"normal_companies_{year}.parquet")
        df_year = pd.DataFrame({'lc_company_id': sorted(company_ids)})
        df_year.to_parquet(parquet_file, index=False)
        
        logger.info(f"{year}年企业ID列表已保存: {len(company_ids):,} 个企业")
    
    # 保存汇总统计
    summary_file = os.path.join(output_dir, "yearly_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("企业历年经营状态分析汇总\n")
        f.write("=" * 50 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析年份范围: 2010-2024\n\n")
        
        f.write("各年度正常经营企业数量:\n")
        f.write("-" * 30 + "\n")
        for year in sorted(yearly_normal_companies.keys()):
            count = len(yearly_normal_companies[year])
            f.write(f"{year}年: {count:,} 个企业\n")
    
    logger.info(f"汇总统计已保存: {summary_file}")

def main():
    """主函数"""
    # 配置参数
    data_dir = "processed_company_data_20250729_160206"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"company_status_analysis_{timestamp}"
    
    logger.info("=" * 60)
    logger.info("企业历年经营状态分析开始")
    logger.info("=" * 60)
    logger.info(f"数据目录: {data_dir}")
    logger.info(f"输出目录: {output_dir}")
    
    try:
        # 检查数据目录是否存在
        if not os.path.exists(data_dir):
            raise ValueError(f"数据目录不存在: {data_dir}")
        
        # 1. 加载和预处理数据
        df = load_and_preprocess_data(data_dir)
        
        # 2. 计算各年度正常企业
        yearly_normal_companies = calculate_yearly_normal_companies(df)
        
        # 3. 保存结果
        save_results(yearly_normal_companies, output_dir)
        
        logger.info("=" * 60)
        logger.info("企业历年经营状态分析完成！")
        logger.info(f"结果已保存到目录: {output_dir}")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()
