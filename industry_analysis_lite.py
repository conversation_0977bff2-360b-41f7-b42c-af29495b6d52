#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业行业分布分析脚本（轻量版）
功能：内存优化版本的行业分布分析
"""

import pandas as pd
import os
import glob
from datetime import datetime
import gc

# 行业分类定义
SECONDARY_INDUSTRIES = {'B', 'C', 'D', 'E'}  # 第二产业
TERTIARY_INDUSTRIES = {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}  # 第三产业

def build_industry_mapping_incrementally(data_dir):
    """
    增量构建企业行业映射表（内存优化）
    
    Args:
        data_dir: 数据目录路径
    
    Returns:
        dict: {企业ID: 行业代码}
    """
    print(f"开始构建企业行业映射表: {data_dir}")
    
    parquet_files = glob.glob(os.path.join(data_dir, "*.parquet"))
    print(f"找到 {len(parquet_files)} 个parquet文件")
    
    industry_mapping = {}
    total_processed = 0
    
    for i, file_path in enumerate(parquet_files, 1):
        print(f"处理文件 {i}/{len(parquet_files)}: {os.path.basename(file_path)}")
        
        try:
            # 只读取需要的字段
            df = pd.read_parquet(file_path, columns=['lc_company_id', 'industry_l1_code'])
            
            # 过滤有效记录
            df = df.dropna(subset=['industry_l1_code'])

            # 高效批量创建字典并更新映射表
            batch_mapping = dict(zip(df['lc_company_id'], df['industry_l1_code']))
            industry_mapping.update(batch_mapping)
            
            total_processed += len(df)
            
            # 清理内存
            del df
            
            # 每20个文件进行垃圾回收
            if i % 20 == 0:
                gc.collect()
                print(f"  已处理 {i} 个文件，累计企业: {len(industry_mapping):,}")
                
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue
    
    print(f"映射表构建完成: {len(industry_mapping):,} 个企业")
    return industry_mapping

def analyze_year_by_year(analysis_dir, industry_mapping):
    """
    逐年分析行业分布（内存优化）
    
    Args:
        analysis_dir: 分析结果目录
        industry_mapping: 企业行业映射字典
    
    Returns:
        list: 分析结果
    """
    print(f"开始逐年分析: {analysis_dir}")
    
    results = []
    txt_files = glob.glob(os.path.join(analysis_dir, "normal_companies_*.txt"))
    
    for file_path in sorted(txt_files):
        filename = os.path.basename(file_path)
        year_str = filename.replace('normal_companies_', '').replace('.txt', '')
        
        try:
            year = int(year_str)
            print(f"分析 {year} 年...")
            
            # 读取企业ID列表
            with open(file_path, 'r', encoding='utf-8') as f:
                company_ids = [line.strip() for line in f if line.strip()]
            
            if len(company_ids) == 0:
                print(f"  跳过空文件: {filename}")
                continue
            
            total_companies = len(company_ids)
            secondary_count = 0
            tertiary_count = 0
            unknown_count = 0
            
            # 逐个分析企业行业
            for company_id in company_ids:
                industry_code = industry_mapping.get(company_id)
                
                if industry_code is None:
                    unknown_count += 1
                elif industry_code in SECONDARY_INDUSTRIES:
                    secondary_count += 1
                elif industry_code in TERTIARY_INDUSTRIES:
                    tertiary_count += 1
                else:
                    unknown_count += 1
            
            # 计算占比
            secondary_ratio = (secondary_count / total_companies * 100) if total_companies > 0 else 0
            tertiary_ratio = (tertiary_count / total_companies * 100) if total_companies > 0 else 0
            unknown_ratio = (unknown_count / total_companies * 100) if total_companies > 0 else 0
            
            result = {
                '年份': year,
                '总企业数量': total_companies,
                '第二产业数量': secondary_count,
                '第二产业占比(%)': round(secondary_ratio, 2),
                '第三产业数量': tertiary_count,
                '第三产业占比(%)': round(tertiary_ratio, 2),
                '未知行业数量': unknown_count,
                '未知行业占比(%)': round(unknown_ratio, 2)
            }
            
            results.append(result)
            
            print(f"  {year}年: 总企业{total_companies:,}, 第二产业{secondary_count:,}({secondary_ratio:.1f}%), "
                  f"第三产业{tertiary_count:,}({tertiary_ratio:.1f}%), 未知{unknown_count:,}({unknown_ratio:.1f}%)")
            
        except ValueError:
            print(f"无法解析年份: {filename}")
            continue
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")
            continue
    
    return results

def save_to_excel_lite(results, output_file):
    """保存结果到Excel（轻量版）"""
    print(f"保存结果到: {output_file}")
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 调整列顺序
    columns = ['年份', '总企业数量', '第二产业数量', '第二产业占比(%)', 
               '第三产业数量', '第三产业占比(%)', '未知行业数量', '未知行业占比(%)']
    df = df[columns]
    
    # 保存Excel
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='行业分布分析', index=False)
        
        # 添加说明
        info_data = [
            ['第二产业', 'B, C, D, E', '采矿业、制造业、电力热力燃气及水生产和供应业、建筑业'],
            ['第三产业', 'F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T', '服务业（批发零售、交通运输、住宿餐饮等）']
        ]
        info_df = pd.DataFrame(info_data, columns=['产业类别', '行业代码', '说明'])
        info_df.to_excel(writer, sheet_name='分类说明', index=False)
    
    print(f"Excel文件保存成功: {output_file}")

def create_summary_csv(results, output_dir):
    """创建CSV格式的汇总文件"""
    csv_file = os.path.join(output_dir, f"industry_summary_{datetime.now().strftime('%Y%m%d')}.csv")
    
    df = pd.DataFrame(results)
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    print(f"CSV汇总文件: {csv_file}")
    return csv_file

def main():
    """主函数"""
    data_dir = "processed_company_data_20250729_160206"
    analysis_dir = "company_status_analysis_20250729_163829"
    timestamp = datetime.now().strftime("%Y%m%d")
    output_file = f"industry_analysis_by_year_{timestamp}.xlsx"
    
    print("=" * 60)
    print("企业行业分布分析（轻量版）")
    print("=" * 60)
    
    try:
        # 检查目录
        if not os.path.exists(data_dir):
            raise ValueError(f"数据目录不存在: {data_dir}")
        
        if not os.path.exists(analysis_dir):
            raise ValueError(f"分析结果目录不存在: {analysis_dir}")
        
        # 1. 构建行业映射表
        print("步骤1: 构建企业行业映射表...")
        industry_mapping = build_industry_mapping_incrementally(data_dir)
        
        # 2. 逐年分析
        print("\n步骤2: 逐年分析行业分布...")
        results = analyze_year_by_year(analysis_dir, industry_mapping)
        
        if not results:
            raise ValueError("未找到任何有效的年份数据")
        
        # 3. 保存结果
        print("\n步骤3: 保存分析结果...")
        save_to_excel_lite(results, output_file)
        
        # 4. 创建CSV汇总
        csv_file = create_summary_csv(results, ".")
        
        print("=" * 60)
        print("分析完成！")
        print(f"Excel文件: {output_file}")
        print(f"CSV文件: {csv_file}")
        print("=" * 60)
        
        # 显示概览
        print("\n结果概览:")
        for result in results:
            year = result['年份']
            total = result['总企业数量']
            sec_pct = result['第二产业占比(%)']
            ter_pct = result['第三产业占比(%)']
            print(f"  {year}年: {total:,}企业, 第二产业{sec_pct}%, 第三产业{ter_pct}%")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
