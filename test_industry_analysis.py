#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试企业行业分析逻辑
验证行业分类和计算是否正确
"""

import pandas as pd
import os

def test_industry_classification():
    """测试行业分类逻辑"""
    print("测试行业分类逻辑...")
    
    # 行业分类定义
    secondary = {'B', 'C', 'D', 'E'}  # 第二产业
    tertiary = {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}  # 第三产业
    
    print(f"第二产业代码: {sorted(secondary)}")
    print(f"第三产业代码: {sorted(tertiary)}")
    
    # 测试样本数据
    test_companies = {
        'company1': 'B',  # 第二产业
        'company2': 'C',  # 第二产业
        'company3': 'F',  # 第三产业
        'company4': 'L',  # 第三产业
        'company5': 'A',  # 第一产业（应归为未知）
        'company6': None, # 无行业信息
    }
    
    secondary_count = 0
    tertiary_count = 0
    unknown_count = 0
    
    for company_id, industry_code in test_companies.items():
        if industry_code is None:
            unknown_count += 1
            print(f"{company_id}: {industry_code} -> 未知行业")
        elif industry_code in secondary:
            secondary_count += 1
            print(f"{company_id}: {industry_code} -> 第二产业")
        elif industry_code in tertiary:
            tertiary_count += 1
            print(f"{company_id}: {industry_code} -> 第三产业")
        else:
            unknown_count += 1
            print(f"{company_id}: {industry_code} -> 未知行业")
    
    total = len(test_companies)
    print(f"\n测试结果:")
    print(f"总企业数: {total}")
    print(f"第二产业: {secondary_count} ({secondary_count/total*100:.1f}%)")
    print(f"第三产业: {tertiary_count} ({tertiary_count/total*100:.1f}%)")
    print(f"未知行业: {unknown_count} ({unknown_count/total*100:.1f}%)")

def test_with_real_data():
    """使用真实数据进行测试"""
    print("\n" + "="*50)
    print("使用真实数据测试...")
    
    # 检查数据文件
    data_file = "processed_company_data_20250729_160206/batch_001.parquet"
    if not os.path.exists(data_file):
        print(f"测试数据文件不存在: {data_file}")
        return
    
    # 读取样本数据
    print(f"读取测试数据: {data_file}")
    df = pd.read_parquet(data_file, columns=['lc_company_id', 'industry_l1_code'])
    
    # 取前1000条记录进行测试
    df_sample = df.head(1000).copy()
    print(f"样本数据: {len(df_sample)} 条记录")
    
    # 行业分类
    secondary = {'B', 'C', 'D', 'E'}
    tertiary = {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}
    
    # 统计分析
    total_count = len(df_sample)
    secondary_count = len(df_sample[df_sample['industry_l1_code'].isin(secondary)])
    tertiary_count = len(df_sample[df_sample['industry_l1_code'].isin(tertiary)])
    unknown_count = total_count - secondary_count - tertiary_count
    
    print(f"\n样本分析结果:")
    print(f"总企业数: {total_count:,}")
    print(f"第二产业: {secondary_count:,} ({secondary_count/total_count*100:.2f}%)")
    print(f"第三产业: {tertiary_count:,} ({tertiary_count/total_count*100:.2f}%)")
    print(f"未知行业: {unknown_count:,} ({unknown_count/total_count*100:.2f}%)")
    
    # 显示行业分布
    print(f"\n行业代码分布:")
    industry_dist = df_sample['industry_l1_code'].value_counts().sort_index()
    for code, count in industry_dist.items():
        category = "第二产业" if code in secondary else "第三产业" if code in tertiary else "其他"
        print(f"  {code}: {count:,} ({category})")

def test_year_file_reading():
    """测试年份文件读取"""
    print("\n" + "="*50)
    print("测试年份文件读取...")
    
    analysis_dir = "company_status_analysis_20250729_163829"
    if not os.path.exists(analysis_dir):
        print(f"分析结果目录不存在: {analysis_dir}")
        return
    
    # 测试读取一个年份文件
    test_file = os.path.join(analysis_dir, "normal_companies_2017.txt")
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print(f"读取测试文件: {test_file}")
    
    with open(test_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 取前10行进行测试
    sample_ids = [line.strip() for line in lines[:10] if line.strip()]
    
    print(f"文件总行数: {len(lines):,}")
    print(f"样本企业ID (前10个):")
    for i, company_id in enumerate(sample_ids, 1):
        print(f"  {i}. {company_id}")
    
    # 验证ID格式
    valid_ids = [id for id in sample_ids if len(id) == 32]  # 假设ID长度为32
    print(f"有效ID数量: {len(valid_ids)}/{len(sample_ids)}")

def main():
    """主测试函数"""
    print("企业行业分析逻辑测试")
    print("=" * 50)
    
    # 1. 测试行业分类逻辑
    test_industry_classification()
    
    # 2. 使用真实数据测试
    test_with_real_data()
    
    # 3. 测试年份文件读取
    test_year_file_reading()
    
    print("\n" + "="*50)
    print("✅ 所有测试完成！")

if __name__ == "__main__":
    main()
