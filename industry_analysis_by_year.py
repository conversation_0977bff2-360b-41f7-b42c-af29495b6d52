#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业行业分布分析脚本
功能：分析各年度第二产业和第三产业企业数量及占比
"""

import pandas as pd
import os
import glob
from datetime import datetime
import gc
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('industry_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 行业分类定义
INDUSTRY_CLASSIFICATION = {
    'secondary': {'B', 'C', 'D', 'E'},  # 第二产业
    'tertiary': {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}  # 第三产业
}

def load_industry_mapping(data_dir):
    """
    加载企业行业映射表
    
    Args:
        data_dir: 数据目录路径
    
    Returns:
        dict: {企业ID: 行业代码}
    """
    logger.info(f"开始加载企业行业映射表: {data_dir}")
    
    parquet_files = glob.glob(os.path.join(data_dir, "*.parquet"))
    logger.info(f"找到 {len(parquet_files)} 个parquet文件")
    
    if not parquet_files:
        raise ValueError(f"在目录 {data_dir} 中未找到任何parquet文件")
    
    # 存储企业行业映射
    industry_mapping = {}
    total_processed = 0
    
    for i, file_path in enumerate(parquet_files, 1):
        logger.info(f"处理文件 {i}/{len(parquet_files)}: {os.path.basename(file_path)}")
        
        try:
            # 只读取需要的两个字段
            df = pd.read_parquet(file_path, columns=['lc_company_id', 'industry_l1_code'])
            
            # 过滤掉行业代码为空的记录
            df = df.dropna(subset=['industry_l1_code'])
            
            # 转换为字典并更新映射表
            batch_mapping = dict(zip(df['lc_company_id'], df['industry_l1_code']))
            industry_mapping.update(batch_mapping)
            
            total_processed += len(df)
            
            # 清理内存
            del df, batch_mapping
            
            # 每处理20个文件进行一次垃圾回收
            if i % 20 == 0:
                gc.collect()
                logger.info(f"已处理 {i} 个文件，累计企业: {len(industry_mapping):,}")
                
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
            continue
    
    logger.info(f"企业行业映射表加载完成")
    logger.info(f"总处理记录: {total_processed:,}")
    logger.info(f"有效企业数量: {len(industry_mapping):,}")
    
    return industry_mapping

def load_yearly_companies(analysis_dir):
    """
    加载各年度正常企业ID列表
    
    Args:
        analysis_dir: 企业状态分析结果目录
    
    Returns:
        dict: {年份: 企业ID集合}
    """
    logger.info(f"开始加载各年度企业ID列表: {analysis_dir}")
    
    yearly_companies = {}
    
    # 查找所有年份文件
    txt_files = glob.glob(os.path.join(analysis_dir, "normal_companies_*.txt"))
    
    for file_path in txt_files:
        # 从文件名提取年份
        filename = os.path.basename(file_path)
        year_str = filename.replace('normal_companies_', '').replace('.txt', '')
        
        try:
            year = int(year_str)
            
            # 读取企业ID列表
            with open(file_path, 'r', encoding='utf-8') as f:
                company_ids = {line.strip() for line in f if line.strip()}
            
            # 跳过空文件
            if len(company_ids) == 0:
                logger.warning(f"跳过空文件: {filename}")
                continue
            
            yearly_companies[year] = company_ids
            logger.info(f"{year}年: {len(company_ids):,} 个正常企业")
            
        except ValueError:
            logger.warning(f"无法解析年份: {filename}")
            continue
        except Exception as e:
            logger.error(f"读取文件 {file_path} 时出错: {str(e)}")
            continue
    
    logger.info(f"成功加载 {len(yearly_companies)} 个年份的数据")
    return yearly_companies

def analyze_industry_distribution(yearly_companies, industry_mapping):
    """
    分析各年度行业分布
    
    Args:
        yearly_companies: 各年度企业ID字典
        industry_mapping: 企业行业映射字典
    
    Returns:
        list: 分析结果列表
    """
    logger.info("开始分析各年度行业分布...")
    
    results = []
    
    for year in sorted(yearly_companies.keys()):
        logger.info(f"分析 {year} 年...")
        
        company_ids = yearly_companies[year]
        total_companies = len(company_ids)
        
        # 统计各行业企业数量
        secondary_count = 0  # 第二产业
        tertiary_count = 0   # 第三产业
        unknown_count = 0    # 未知行业
        
        for company_id in company_ids:
            industry_code = industry_mapping.get(company_id)
            
            if industry_code is None:
                unknown_count += 1
            elif industry_code in INDUSTRY_CLASSIFICATION['secondary']:
                secondary_count += 1
            elif industry_code in INDUSTRY_CLASSIFICATION['tertiary']:
                tertiary_count += 1
            else:
                unknown_count += 1
        
        # 计算占比
        secondary_ratio = (secondary_count / total_companies * 100) if total_companies > 0 else 0
        tertiary_ratio = (tertiary_count / total_companies * 100) if total_companies > 0 else 0
        unknown_ratio = (unknown_count / total_companies * 100) if total_companies > 0 else 0
        
        # 记录结果
        result = {
            '年份': year,
            '第二产业数量': secondary_count,
            '第三产业数量': tertiary_count,
            '第二产业占比(%)': round(secondary_ratio, 2),
            '第三产业占比(%)': round(tertiary_ratio, 2),
            '总企业数量': total_companies,
            '未知行业数量': unknown_count,
            '未知行业占比(%)': round(unknown_ratio, 2)
        }
        
        results.append(result)
        
        logger.info(f"  {year}年结果:")
        logger.info(f"    总企业数: {total_companies:,}")
        logger.info(f"    第二产业: {secondary_count:,} ({secondary_ratio:.2f}%)")
        logger.info(f"    第三产业: {tertiary_count:,} ({tertiary_ratio:.2f}%)")
        logger.info(f"    未知行业: {unknown_count:,} ({unknown_ratio:.2f}%)")
    
    return results

def save_results_to_excel(results, output_file):
    """
    保存结果到Excel文件
    
    Args:
        results: 分析结果列表
        output_file: 输出文件路径
    """
    logger.info(f"保存结果到Excel文件: {output_file}")
    
    # 转换为DataFrame
    df = pd.DataFrame(results)
    
    # 调整列顺序
    columns_order = [
        '年份', '总企业数量', 
        '第二产业数量', '第二产业占比(%)', 
        '第三产业数量', '第三产业占比(%)',
        '未知行业数量', '未知行业占比(%)'
    ]
    df = df[columns_order]
    
    # 保存到Excel
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主要结果表
        df.to_excel(writer, sheet_name='行业分布分析', index=False)
        
        # 添加行业分类说明表
        classification_data = [
            {'产业类别': '第二产业', '行业代码': 'B, C, D, E', '说明': '采矿业、制造业、电力热力燃气及水生产和供应业、建筑业'},
            {'产业类别': '第三产业', '行业代码': 'F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T', '说明': '批发和零售业、交通运输仓储和邮政业、住宿和餐饮业等服务业'}
        ]
        classification_df = pd.DataFrame(classification_data)
        classification_df.to_excel(writer, sheet_name='行业分类说明', index=False)
    
    logger.info(f"Excel文件保存成功: {output_file}")

def main():
    """主函数"""
    # 配置参数
    data_dir = "processed_company_data_20250729_160206"
    analysis_dir = "company_status_analysis_20250729_163829"
    timestamp = datetime.now().strftime("%Y%m%d")
    output_file = f"industry_analysis_by_year_{timestamp}.xlsx"
    
    logger.info("=" * 60)
    logger.info("企业行业分布分析开始")
    logger.info("=" * 60)
    logger.info(f"数据目录: {data_dir}")
    logger.info(f"分析结果目录: {analysis_dir}")
    logger.info(f"输出文件: {output_file}")
    
    try:
        # 检查目录是否存在
        if not os.path.exists(data_dir):
            raise ValueError(f"数据目录不存在: {data_dir}")
        
        if not os.path.exists(analysis_dir):
            raise ValueError(f"分析结果目录不存在: {analysis_dir}")
        
        # 1. 加载企业行业映射表
        industry_mapping = load_industry_mapping(data_dir)
        
        # 2. 加载各年度企业ID列表
        yearly_companies = load_yearly_companies(analysis_dir)
        
        if not yearly_companies:
            raise ValueError("未找到任何年份的企业数据")
        
        # 3. 分析行业分布
        results = analyze_industry_distribution(yearly_companies, industry_mapping)
        
        # 4. 保存结果
        save_results_to_excel(results, output_file)
        
        logger.info("=" * 60)
        logger.info("企业行业分布分析完成！")
        logger.info(f"结果已保存到: {output_file}")
        logger.info("=" * 60)
        
        # 输出简要统计
        logger.info("分析结果概览:")
        for result in results:
            year = result['年份']
            total = result['总企业数量']
            secondary = result['第二产业占比(%)']
            tertiary = result['第三产业占比(%)']
            logger.info(f"  {year}年: 总企业{total:,}个, 第二产业{secondary}%, 第三产业{tertiary}%")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()
