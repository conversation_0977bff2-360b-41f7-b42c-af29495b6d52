# Parquet文件批量处理脚本

## 功能说明
批量处理指定目录下的所有Parquet文件，提取指定字段，按100万条记录分批保存。

## 文件说明

### 1. batch_process_parquet.py (完整版)
- **功能**: 完整的批量处理脚本，包含详细的日志记录和错误处理
- **特点**: 
  - 完整的日志系统，同时输出到文件和控制台
  - 详细的错误处理和异常捕获
  - 内存优化的分块读取
  - 进度提示和统计信息

### 2. simple_batch_process.py (简化版)
- **功能**: 简化版批量处理脚本，代码更简洁
- **特点**:
  - 代码简洁，易于理解和修改
  - 基本的错误处理
  - 控制台输出进度信息

### 3. memory_optimized_batch_process.py (内存优化版)
- **功能**: 专门针对大量文件（如3328个文件）优化的版本
- **特点**:
  - 强制垃圾回收，优化内存使用
  - 适合处理大量小文件
  - 详细的进度报告（每100个文件）
  - 自动内存清理机制

### 4. test_timestamp_handling.py (测试脚本)
- **功能**: 测试时间戳处理功能是否正常工作
- **特点**:
  - 测试之前出错的特定文件
  - 随机测试其他文件
  - 详细的时间戳字段统计信息

## 企业状态分析脚本

### 5. company_status_analysis.py (企业状态分析完整版)
- **功能**: 分析2010-2024年每年的正常经营企业数量和ID列表
- **特点**:
  - 完整的数据预处理和验证
  - 详细的日志记录
  - 同时输出txt和parquet格式
  - 包含汇总统计信息

### 6. company_status_analysis_lite.py (企业状态分析轻量版)
- **功能**: 内存优化版本的企业状态分析
- **特点**:
  - 分批处理，内存占用更少
  - 适合大数据量处理
  - 只输出txt格式，节省空间
  - 简化的进度提示

### 7. validate_analysis_results.py (结果验证脚本)
- **功能**: 验证企业状态分析结果的合理性
- **特点**:
  - 趋势逻辑验证
  - 生成可视化图表
  - 抽样验证功能
  - 详细的验证报告

## 企业行业分布分析脚本

### 8. industry_analysis_by_year.py (行业分析完整版)
- **功能**: 分析各年度第二产业和第三产业企业数量及占比
- **特点**:
  - 完整的企业行业映射表构建
  - 详细的日志记录系统
  - 输出Excel和分类说明
  - 包含未知行业统计

### 9. industry_analysis_lite.py (行业分析轻量版)
- **功能**: 内存优化版本的行业分布分析
- **特点**:
  - 增量构建映射表，内存占用少
  - 逐年分析，避免大数据集加载
  - 同时输出Excel和CSV格式
  - 适合大数据量处理

### 10. test_industry_analysis.py (行业分析测试脚本)
- **功能**: 测试行业分析逻辑的正确性
- **特点**:
  - 验证行业分类逻辑
  - 使用真实数据测试
  - 验证年份文件读取
  - 显示行业分布统计

## 使用方法

### 环境要求
```bash
pip install pandas pyarrow
```

### 运行脚本
```bash
# 运行完整版
python batch_process_parquet.py

# 或运行简化版
python simple_batch_process.py

# 或运行内存优化版（推荐用于大量文件）
python memory_optimized_batch_process.py

# 测试时间戳处理功能
python test_timestamp_handling.py

# 企业状态分析（推荐使用轻量版）
python company_status_analysis_lite.py

# 或使用完整版
python company_status_analysis.py

# 验证分析结果
python validate_analysis_results.py

# 行业分布分析（推荐使用轻量版）
python industry_analysis_lite.py

# 或使用完整版
python industry_analysis_by_year.py

# 测试行业分析逻辑
python test_industry_analysis.py
```

## 配置参数

### 源数据路径
```python
source_path = r"F:\蕾奥工作\20.数据库转到本地\output\company_general_info_v3"
```

### 提取字段
```python
columns = [
    'lc_company_id',
    'company_name', 
    'company_status_clean',
    'industry_l1_code',
    'establish_date',
    'cancel_date',
    'revoke_date'
]
```

### 批次大小
```python
batch_size = 1000000  # 100万条记录
```

## 输出说明

### 输出目录
- 格式: `processed_company_data_YYYYMMDD_HHMMSS`
- 示例: `processed_company_data_20241129_143022`

### 输出文件
- 格式: `batch_001.parquet`, `batch_002.parquet`, ...
- 每个文件包含最多100万条记录
- 最后一个文件可能少于100万条记录

## 性能优化

1. **内存管理**: 及时释放已处理的数据，保持内存使用稳定
2. **批量写入**: 累积到指定数量后批量写入，减少I/O操作
3. **垃圾回收**: 定期强制垃圾回收，优化内存使用（内存优化版）
4. **进度监控**: 处理大量文件时提供详细的进度报告

## 修正说明

### 第一次修正: chunksize参数问题
- 移除了 `pd.read_parquet()` 中不支持的 `chunksize` 参数
- 直接读取整个parquet文件（parquet文件通常已压缩，单个文件不会太大）
- 对于特别大的单个文件，采用分批保存策略
- 增强内存管理，定期进行垃圾回收

### 第二次修正: 时间戳转换问题
**问题**: 时间戳从微秒精度转换为纳秒精度时超出范围
**解决方案**:
- 添加时间戳转换错误的捕获和处理
- 使用 `pyarrow.parquet` 直接读取，设置 `timestamp_as_object=True`
- 实现 `clean_timestamp_columns()` 函数处理无效时间戳
- 超出范围的时间戳自动转换为 `NaT` (Not a Time)
- 详细的时间戳处理日志记录

### 第三次修正: 集合操作TypeError
**问题**: 企业状态分析脚本中集合操作使用了不支持的 `+` 操作符
**错误**: `TypeError: unsupported operand type(s) for +: 'set' and 'set'`
**解决方案**:
- 将 `current_normal - established + cancelled + revoked`
- 修正为 `current_normal - established | cancelled | revoked`
- 使用集合并集操作符 `|` 替代不支持的 `+` 操作符
- 保持业务逻辑不变：减去新成立企业，加回注销和吊销企业

## 企业状态分析说明

### 分析逻辑
企业状态分析采用逆向推算方法：
1. **基准年（2024年）**: 以 `company_status_clean == 1` 的企业为正常企业
2. **逆向计算**: 从2024年向前推算到2010年
3. **计算公式**: 某年正常企业 = 下一年正常企业 - 下一年新成立 + 下一年注销 + 下一年吊销

### 输出文件
- **目录格式**: `company_status_analysis_YYYYMMDD_HHMMSS`
- **企业ID文件**: `normal_companies_YYYY.txt` (每行一个企业ID)
- **Parquet文件**: `normal_companies_YYYY.parquet` (仅完整版)
- **汇总统计**: `yearly_summary.txt`
- **验证报告**: `validation_report.txt` (验证脚本生成)
- **趋势图表**: `yearly_trend_chart.png` (验证脚本生成)

## 企业行业分布分析说明

### 行业分类标准
- **第二产业**: B(采矿业)、C(制造业)、D(电力热力燃气及水生产和供应业)、E(建筑业)
- **第三产业**: F(批发和零售业)、G(交通运输仓储和邮政业)、H(住宿和餐饮业)、I(信息传输软件和信息技术服务业)、J(金融业)、K(房地产业)、L(租赁和商务服务业)、M(科学研究和技术服务业)、N(水利环境和公共设施管理业)、O(居民服务修理和其他服务业)、P(教育)、Q(卫生和社会工作)、R(文化体育和娱乐业)、S(公共管理社会保障和社会组织)、T(国际组织)

### 分析指标
- **企业数量**: 各产业正常经营企业的绝对数量
- **占比**: 各产业企业数量占当年总正常企业数的百分比
- **未知行业**: 无法匹配行业代码或属于第一产业(A)的企业

### 输出文件
- **Excel文件**: `industry_analysis_by_year_YYYYMMDD.xlsx`
- **CSV文件**: `industry_summary_YYYYMMDD.csv` (仅轻量版)
- **包含工作表**: 行业分布分析、行业分类说明

## 注意事项

1. 确保源路径存在且包含parquet文件
2. 确保有足够的磁盘空间存储输出文件
3. 如果源文件中缺少某些字段，脚本会自动添加空值
4. 建议在处理大量数据前先用小样本测试

## 错误处理

- 文件读取错误：跳过有问题的文件，继续处理其他文件
- 字段缺失：自动添加空值，确保输出格式一致
- 内存不足：使用分块读取，控制内存使用

## 日志文件 (仅完整版)
- 文件名: `batch_process.log`
- 包含详细的处理过程和错误信息
- 支持中文编码
