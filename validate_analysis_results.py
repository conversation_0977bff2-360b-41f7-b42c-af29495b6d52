#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业状态分析结果验证脚本
功能：验证分析结果的合理性和一致性
"""

import pandas as pd
import os
import glob
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
matplotlib.rcParams['axes.unicode_minus'] = False

def load_analysis_results(result_dir):
    """加载分析结果"""
    print(f"加载分析结果: {result_dir}")
    
    yearly_counts = {}
    
    # 读取各年度企业数量
    for year in range(2010, 2025):
        txt_file = os.path.join(result_dir, f"normal_companies_{year}.txt")
        if os.path.exists(txt_file):
            with open(txt_file, 'r', encoding='utf-8') as f:
                company_ids = [line.strip() for line in f if line.strip()]
                yearly_counts[year] = len(company_ids)
        else:
            print(f"警告: 文件不存在 {txt_file}")
    
    return yearly_counts

def validate_trend_logic(yearly_counts):
    """验证趋势逻辑的合理性"""
    print("\n验证趋势逻辑...")
    
    years = sorted(yearly_counts.keys())
    
    # 检查是否有异常的大幅波动
    for i in range(1, len(years)):
        prev_year = years[i-1]
        curr_year = years[i]
        
        prev_count = yearly_counts[prev_year]
        curr_count = yearly_counts[curr_year]
        
        change = curr_count - prev_count
        change_rate = (change / prev_count) * 100 if prev_count > 0 else 0
        
        print(f"{prev_year} -> {curr_year}: {prev_count:,} -> {curr_count:,} "
              f"(变化: {change:+,}, {change_rate:+.1f}%)")
        
        # 检查异常变化（超过50%的变化可能有问题）
        if abs(change_rate) > 50:
            print(f"  ⚠️ 警告: {prev_year}到{curr_year}年变化幅度过大 ({change_rate:+.1f}%)")

def create_trend_chart(yearly_counts, output_dir):
    """创建趋势图表"""
    print("\n创建趋势图表...")
    
    years = sorted(yearly_counts.keys())
    counts = [yearly_counts[year] for year in years]
    
    plt.figure(figsize=(12, 8))
    plt.plot(years, counts, marker='o', linewidth=2, markersize=6)
    plt.title('企业正常经营数量历年趋势 (2010-2024)', fontsize=16, fontweight='bold')
    plt.xlabel('年份', fontsize=12)
    plt.ylabel('正常经营企业数量', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for year, count in zip(years, counts):
        plt.annotate(f'{count:,}', (year, count), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=9)
    
    plt.xticks(years, rotation=45)
    plt.tight_layout()
    
    chart_file = os.path.join(output_dir, 'yearly_trend_chart.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"趋势图表已保存: {chart_file}")

def sample_validate_with_source(yearly_counts, source_data_dir):
    """使用源数据进行抽样验证"""
    print("\n进行抽样验证...")
    
    # 随机选择几个文件进行验证
    parquet_files = glob.glob(os.path.join(source_data_dir, "*.parquet"))
    if not parquet_files:
        print("未找到源数据文件，跳过抽样验证")
        return
    
    # 选择前3个文件进行快速验证
    sample_files = parquet_files[:3]
    
    sample_normal_2024 = set()
    sample_events = {'established': {}, 'cancelled': {}, 'revoked': {}}
    
    for file_path in sample_files:
        print(f"验证文件: {os.path.basename(file_path)}")
        
        try:
            df = pd.read_parquet(file_path)
            
            # 过滤有效记录
            date_columns = ['establish_date', 'cancel_date', 'revoke_date']
            valid_mask = ~(df[date_columns].isna().all(axis=1))
            df = df[valid_mask]
            
            if len(df) == 0:
                continue
            
            # 2024年正常企业
            normal_companies = df[df['company_status_clean'] == 1]['lc_company_id'].unique()
            sample_normal_2024.update(normal_companies)
            
            # 提取年份和事件
            df['establish_year'] = df['establish_date'].dt.year
            df['cancel_year'] = df['cancel_date'].dt.year
            df['revoke_year'] = df['revoke_date'].dt.year
            
            # 统计各年事件
            for year in [2023, 2024]:  # 只验证关键年份
                established = len(df[df['establish_year'] == year])
                cancelled = len(df[df['cancel_year'] == year])
                revoked = len(df[df['revoke_year'] == year])
                
                if year not in sample_events['established']:
                    sample_events['established'][year] = 0
                    sample_events['cancelled'][year] = 0
                    sample_events['revoked'][year] = 0
                
                sample_events['established'][year] += established
                sample_events['cancelled'][year] += cancelled
                sample_events['revoked'][year] += revoked
            
        except Exception as e:
            print(f"验证文件 {file_path} 时出错: {e}")
            continue
    
    print(f"抽样验证结果:")
    print(f"  样本2024年正常企业数量: {len(sample_normal_2024):,}")
    print(f"  分析结果2024年正常企业数量: {yearly_counts.get(2024, 0):,}")
    
    for year in [2023, 2024]:
        if year in sample_events['established']:
            print(f"  {year}年样本事件统计:")
            print(f"    成立: {sample_events['established'][year]:,}")
            print(f"    注销: {sample_events['cancelled'][year]:,}")
            print(f"    吊销: {sample_events['revoked'][year]:,}")

def generate_validation_report(yearly_counts, output_dir):
    """生成验证报告"""
    print("\n生成验证报告...")
    
    report_file = os.path.join(output_dir, 'validation_report.txt')
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("企业状态分析结果验证报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("1. 各年度企业数量统计\n")
        f.write("-" * 30 + "\n")
        
        years = sorted(yearly_counts.keys())
        for year in years:
            f.write(f"{year}年: {yearly_counts[year]:,} 个正常企业\n")
        
        f.write(f"\n2. 年度变化分析\n")
        f.write("-" * 30 + "\n")
        
        for i in range(1, len(years)):
            prev_year = years[i-1]
            curr_year = years[i]
            
            prev_count = yearly_counts[prev_year]
            curr_count = yearly_counts[curr_year]
            
            change = curr_count - prev_count
            change_rate = (change / prev_count) * 100 if prev_count > 0 else 0
            
            f.write(f"{prev_year}->{curr_year}: {change:+,} ({change_rate:+.1f}%)\n")
        
        f.write(f"\n3. 关键指标\n")
        f.write("-" * 30 + "\n")
        f.write(f"最高年份: {max(yearly_counts, key=yearly_counts.get)} "
                f"({yearly_counts[max(yearly_counts, key=yearly_counts.get)]:,})\n")
        f.write(f"最低年份: {min(yearly_counts, key=yearly_counts.get)} "
                f"({yearly_counts[min(yearly_counts, key=yearly_counts.get)]:,})\n")
        
        total_change = yearly_counts[max(years)] - yearly_counts[min(years)]
        total_rate = (total_change / yearly_counts[min(years)]) * 100
        f.write(f"总体变化: {total_change:+,} ({total_rate:+.1f}%)\n")
    
    print(f"验证报告已保存: {report_file}")

def main():
    """主函数"""
    # 查找最新的分析结果目录
    analysis_dirs = glob.glob("company_status_analysis_*")
    if not analysis_dirs:
        print("未找到分析结果目录")
        return
    
    latest_dir = max(analysis_dirs)
    print(f"使用最新的分析结果目录: {latest_dir}")
    
    source_data_dir = "processed_company_data_20250729_160206"
    
    try:
        # 1. 加载分析结果
        yearly_counts = load_analysis_results(latest_dir)
        
        if not yearly_counts:
            print("未找到有效的分析结果")
            return
        
        # 2. 验证趋势逻辑
        validate_trend_logic(yearly_counts)
        
        # 3. 创建趋势图表
        create_trend_chart(yearly_counts, latest_dir)
        
        # 4. 抽样验证
        if os.path.exists(source_data_dir):
            sample_validate_with_source(yearly_counts, source_data_dir)
        
        # 5. 生成验证报告
        generate_validation_report(yearly_counts, latest_dir)
        
        print("\n验证完成！")
        
    except Exception as e:
        print(f"验证过程中发生错误: {e}")

if __name__ == "__main__":
    main()
