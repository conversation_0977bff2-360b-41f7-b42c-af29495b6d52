#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间戳处理功能的脚本
用于验证修正后的parquet读取是否能正确处理时间戳问题
"""

import pandas as pd
import os
import glob
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clean_timestamp_columns(df, timestamp_columns=['establish_date', 'cancel_date', 'revoke_date']):
    """清理时间戳列，处理无效的时间戳数据"""
    for col in timestamp_columns:
        if col in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    # 检查超出范围的时间戳
                    invalid_mask = (df[col] < pd.Timestamp('1677-09-21')) | (df[col] > pd.Timestamp('2262-04-11'))
                    if invalid_mask.any():
                        invalid_count = invalid_mask.sum()
                        logger.warning(f"列 {col} 中发现 {invalid_count} 个超出范围的时间戳，将设置为NaT")
                        df.loc[invalid_mask, col] = pd.NaT
                else:
                    if df[col].dtype == 'object':
                        df[col] = pd.to_datetime(df[col], errors='coerce', utc=False)
                        invalid_count = df[col].isna().sum()
                        if invalid_count > 0:
                            logger.info(f"列 {col} 中有 {invalid_count} 个无效时间戳已转换为NaT")
            except Exception as e:
                logger.warning(f"处理时间戳列 {col} 时出错: {str(e)}，保持原始格式")
    return df

def test_single_file(file_path):
    """测试单个文件的读取"""
    required_columns = [
        'lc_company_id',
        'company_name', 
        'company_status_clean',
        'industry_l1_code',
        'establish_date',
        'cancel_date',
        'revoke_date'
    ]
    
    logger.info(f"测试文件: {os.path.basename(file_path)}")
    
    try:
        # 尝试正常读取
        df = pd.read_parquet(file_path, columns=required_columns)
        logger.info("✅ 正常读取成功")
    except Exception as timestamp_error:
        if "timestamp" in str(timestamp_error).lower() or "out of bounds" in str(timestamp_error).lower():
            logger.warning(f"⚠️ 时间戳转换问题，使用字符串格式读取: {str(timestamp_error)}")
            try:
                import pyarrow.parquet as pq
                table = pq.read_table(file_path, columns=required_columns)
                df = table.to_pandas(timestamp_as_object=True)
                logger.info("✅ 使用pyarrow读取成功")
            except Exception as e:
                logger.error(f"❌ pyarrow读取也失败: {str(e)}")
                return False
        else:
            logger.error(f"❌ 其他读取错误: {str(timestamp_error)}")
            return False
    
    # 检查字段
    missing_cols = [col for col in required_columns if col not in df.columns]
    if missing_cols:
        logger.warning(f"缺少字段: {missing_cols}")
        for col in missing_cols:
            df[col] = None
    
    # 确保字段顺序
    df = df[required_columns]
    
    # 清理时间戳数据
    df = clean_timestamp_columns(df)
    
    # 输出统计信息
    logger.info(f"记录数: {len(df):,}")
    
    # 检查时间戳字段的数据类型和有效性
    for col in ['establish_date', 'cancel_date', 'revoke_date']:
        if col in df.columns:
            logger.info(f"{col}: 数据类型={df[col].dtype}, 非空值={df[col].notna().sum():,}, 空值={df[col].isna().sum():,}")
    
    return True

def test_problematic_files():
    """测试有问题的文件"""
    source_path = r"F:\蕾奥工作\20.数据库转到本地\output\company_general_info_v3"
    
    if not os.path.exists(source_path):
        logger.error(f"源路径不存在: {source_path}")
        return
    
    # 获取所有parquet文件
    parquet_files = glob.glob(os.path.join(source_path, "*.parquet"))
    logger.info(f"找到 {len(parquet_files)} 个parquet文件")
    
    # 测试前几个文件，特别是之前出错的文件
    test_files = [
        "company_general_info_v3_part_000002.parquet",  # 之前出错的文件
        "company_general_info_v3_part_003326.parquet",  # 之前出错的文件
        "company_general_info_v3_part_003327.parquet"   # 之前出错的文件
    ]
    
    success_count = 0
    total_count = 0
    
    for test_file in test_files:
        file_path = os.path.join(source_path, test_file)
        if os.path.exists(file_path):
            total_count += 1
            logger.info(f"\n{'='*50}")
            if test_single_file(file_path):
                success_count += 1
        else:
            logger.warning(f"测试文件不存在: {test_file}")
    
    # 随机测试几个其他文件
    import random
    other_files = [f for f in parquet_files if os.path.basename(f) not in test_files]
    random_files = random.sample(other_files, min(5, len(other_files)))
    
    for file_path in random_files:
        total_count += 1
        logger.info(f"\n{'='*50}")
        if test_single_file(file_path):
            success_count += 1
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {success_count}/{total_count} 个文件成功读取")
    
    if success_count == total_count:
        logger.info("🎉 所有测试文件都能正常读取！")
    else:
        logger.warning(f"⚠️ 有 {total_count - success_count} 个文件读取失败")

if __name__ == "__main__":
    test_problematic_files()
