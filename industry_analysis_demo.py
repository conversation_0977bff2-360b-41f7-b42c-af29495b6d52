#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业行业分布分析演示脚本
使用少量数据快速演示分析功能
"""

import pandas as pd
import os
from datetime import datetime

# 行业分类定义
SECONDARY_INDUSTRIES = {'B', 'C', 'D', 'E'}  # 第二产业
TERTIARY_INDUSTRIES = {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}  # 第三产业

def demo_with_sample_data():
    """使用样本数据进行演示"""
    print("企业行业分布分析演示")
    print("=" * 50)
    
    # 检查数据文件
    data_file = "processed_company_data_20250729_160206/batch_001.parquet"
    if not os.path.exists(data_file):
        print(f"数据文件不存在: {data_file}")
        return
    
    # 读取样本数据（前10000条）
    print("1. 读取样本数据...")
    df = pd.read_parquet(data_file, columns=['lc_company_id', 'industry_l1_code'])
    df_sample = df.head(10000).copy()
    print(f"   样本数据: {len(df_sample):,} 条记录")
    
    # 构建行业映射表
    print("2. 构建行业映射表...")
    industry_mapping = dict(zip(df_sample['lc_company_id'], df_sample['industry_l1_code']))
    print(f"   映射表大小: {len(industry_mapping):,} 个企业")
    
    # 模拟年份数据（使用样本企业ID）
    print("3. 模拟年份企业数据...")
    
    # 从样本中随机选择不同年份的企业
    import random
    random.seed(42)  # 固定随机种子，确保结果可重现
    
    all_company_ids = list(df_sample['lc_company_id'].unique())
    
    # 模拟2015-2017年的企业数据（递增趋势）
    yearly_companies = {
        2015: set(random.sample(all_company_ids, 3000)),
        2016: set(random.sample(all_company_ids, 5000)),
        2017: set(random.sample(all_company_ids, 7000))
    }
    
    for year, companies in yearly_companies.items():
        print(f"   {year}年: {len(companies):,} 个企业")
    
    # 分析各年度行业分布
    print("\n4. 分析行业分布...")
    results = []
    
    for year in sorted(yearly_companies.keys()):
        print(f"\n分析 {year} 年:")
        
        company_ids = yearly_companies[year]
        total_companies = len(company_ids)
        
        secondary_count = 0
        tertiary_count = 0
        unknown_count = 0
        
        # 统计各行业企业数量
        for company_id in company_ids:
            industry_code = industry_mapping.get(company_id)
            
            if industry_code is None:
                unknown_count += 1
            elif industry_code in SECONDARY_INDUSTRIES:
                secondary_count += 1
            elif industry_code in TERTIARY_INDUSTRIES:
                tertiary_count += 1
            else:
                unknown_count += 1
        
        # 计算占比
        secondary_ratio = (secondary_count / total_companies * 100) if total_companies > 0 else 0
        tertiary_ratio = (tertiary_count / total_companies * 100) if total_companies > 0 else 0
        unknown_ratio = (unknown_count / total_companies * 100) if total_companies > 0 else 0
        
        result = {
            '年份': year,
            '总企业数量': total_companies,
            '第二产业数量': secondary_count,
            '第二产业占比(%)': round(secondary_ratio, 2),
            '第三产业数量': tertiary_count,
            '第三产业占比(%)': round(tertiary_ratio, 2),
            '未知行业数量': unknown_count,
            '未知行业占比(%)': round(unknown_ratio, 2)
        }
        
        results.append(result)
        
        print(f"  总企业数: {total_companies:,}")
        print(f"  第二产业: {secondary_count:,} ({secondary_ratio:.2f}%)")
        print(f"  第三产业: {tertiary_count:,} ({tertiary_ratio:.2f}%)")
        print(f"  未知行业: {unknown_count:,} ({unknown_ratio:.2f}%)")
    
    # 保存演示结果
    print("\n5. 保存演示结果...")
    
    # 创建DataFrame
    df_results = pd.DataFrame(results)
    
    # 保存为Excel
    output_file = f"industry_analysis_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主要结果
        df_results.to_excel(writer, sheet_name='行业分布分析', index=False)
        
        # 行业分类说明
        classification_data = [
            {'产业类别': '第二产业', '行业代码': 'B, C, D, E', '说明': '采矿业、制造业、电力热力燃气及水生产和供应业、建筑业'},
            {'产业类别': '第三产业', '行业代码': 'F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T', '说明': '服务业（批发零售、交通运输、住宿餐饮等）'}
        ]
        classification_df = pd.DataFrame(classification_data)
        classification_df.to_excel(writer, sheet_name='行业分类说明', index=False)
        
        # 样本数据统计
        sample_stats = df_sample['industry_l1_code'].value_counts().sort_index()
        sample_df = pd.DataFrame({
            '行业代码': sample_stats.index,
            '企业数量': sample_stats.values,
            '产业类别': [
                '第二产业' if code in SECONDARY_INDUSTRIES 
                else '第三产业' if code in TERTIARY_INDUSTRIES 
                else '其他' 
                for code in sample_stats.index
            ]
        })
        sample_df.to_excel(writer, sheet_name='样本数据统计', index=False)
    
    print(f"   Excel文件已保存: {output_file}")
    
    # 保存CSV
    csv_file = f"industry_analysis_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df_results.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"   CSV文件已保存: {csv_file}")
    
    # 显示汇总
    print("\n6. 演示结果汇总:")
    print("-" * 30)
    for result in results:
        year = result['年份']
        total = result['总企业数量']
        sec_pct = result['第二产业占比(%)']
        ter_pct = result['第三产业占比(%)']
        print(f"{year}年: {total:,}企业, 第二产业{sec_pct}%, 第三产业{ter_pct}%")
    
    print("\n✅ 演示完成！")
    print(f"输出文件: {output_file}, {csv_file}")

def show_real_data_preview():
    """显示真实数据预览"""
    print("\n" + "="*50)
    print("真实数据预览")
    print("="*50)
    
    # 检查年份文件
    analysis_dir = "company_status_analysis_20250729_163829"
    if not os.path.exists(analysis_dir):
        print(f"分析结果目录不存在: {analysis_dir}")
        return
    
    # 显示可用年份
    import glob
    txt_files = glob.glob(os.path.join(analysis_dir, "normal_companies_*.txt"))
    
    print("可用年份数据:")
    for file_path in sorted(txt_files):
        filename = os.path.basename(file_path)
        year_str = filename.replace('normal_companies_', '').replace('.txt', '')
        
        try:
            file_size = os.path.getsize(file_path)
            if file_size > 0:
                # 估算企业数量（假设每个ID约34字节）
                estimated_count = file_size // 34
                print(f"  {year_str}年: 文件大小 {file_size/1024/1024:.1f}MB, 估计企业数 {estimated_count:,}")
            else:
                print(f"  {year_str}年: 空文件")
        except:
            print(f"  {year_str}年: 无法读取")

if __name__ == "__main__":
    # 运行演示
    demo_with_sample_data()
    
    # 显示真实数据预览
    show_real_data_preview()
