#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版批量处理Parquet文件脚本
"""

import pandas as pd
import os
import glob
from datetime import datetime
from pathlib import Path

def clean_timestamp_columns(df, timestamp_columns=['establish_date', 'cancel_date', 'revoke_date']):
    """清理时间戳列，处理无效的时间戳数据"""
    for col in timestamp_columns:
        if col in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    # 检查超出范围的时间戳
                    invalid_mask = (df[col] < pd.Timestamp('1677-09-21')) | (df[col] > pd.Timestamp('2262-04-11'))
                    if invalid_mask.any():
                        print(f"列 {col} 中发现 {invalid_mask.sum()} 个超出范围的时间戳，设置为NaT")
                        df.loc[invalid_mask, col] = pd.NaT
                else:
                    if df[col].dtype == 'object':
                        df[col] = pd.to_datetime(df[col], errors='coerce', utc=False)
                        invalid_count = df[col].isna().sum()
                        if invalid_count > 0:
                            print(f"列 {col} 中有 {invalid_count} 个无效时间戳已转换为NaT")
            except Exception as e:
                print(f"处理时间戳列 {col} 时出错: {e}，保持原始格式")
    return df

def main():
    """主处理函数"""
    # 配置参数
    source_path = r"F:\蕾奥工作\20.数据库转到本地\output\company_general_info_v3"
    batch_size = 1000000  # 100万条记录
    
    # 需要提取的字段
    columns = [
        'lc_company_id',
        'company_name', 
        'company_status_clean',
        'industry_l1_code',
        'establish_date',
        'cancel_date',
        'revoke_date'
    ]
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"processed_company_data_{timestamp}"
    Path(output_dir).mkdir(exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 获取所有parquet文件
    parquet_files = glob.glob(os.path.join(source_path, "*.parquet"))
    print(f"找到 {len(parquet_files)} 个parquet文件")
    
    if not parquet_files:
        print("未找到任何parquet文件")
        return
    
    # 初始化变量
    batch_data = []
    batch_count = 0
    total_processed = 0
    current_size = 0
    
    print("开始处理文件...")
    
    for i, file_path in enumerate(parquet_files, 1):
        print(f"处理文件 {i}/{len(parquet_files)}: {os.path.basename(file_path)}")
        
        try:
            # 直接读取整个文件，处理时间戳转换问题
            try:
                df = pd.read_parquet(file_path, columns=columns)
            except Exception as timestamp_error:
                if "timestamp" in str(timestamp_error).lower() or "out of bounds" in str(timestamp_error).lower():
                    print(f"文件 {os.path.basename(file_path)} 存在时间戳问题，使用字符串格式读取")
                    import pyarrow.parquet as pq
                    table = pq.read_table(file_path, columns=columns)
                    df = table.to_pandas(timestamp_as_object=True)
                else:
                    raise timestamp_error

            # 确保所有字段都存在
            for col in columns:
                if col not in df.columns:
                    df[col] = None

            # 重新排序字段
            df = df[columns]

            # 清理时间戳数据
            df = clean_timestamp_columns(df)

            # 如果单个文件很大，直接分批保存
            if len(df) > batch_size:
                # 先保存当前累积的数据（如果有）
                if batch_data and current_size > 0:
                    current_df = pd.concat(batch_data, ignore_index=True)
                    batch_count += 1
                    output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
                    current_df.to_parquet(output_file, index=False)
                    total_processed += len(current_df)
                    print(f"保存批次 {batch_count}: {len(current_df)} 条记录")

                    # 重置累积数据
                    batch_data = []
                    current_size = 0

                # 将大文件分批保存
                start_idx = 0
                while start_idx < len(df):
                    end_idx = min(start_idx + batch_size, len(df))
                    batch_chunk = df.iloc[start_idx:end_idx].copy()

                    batch_count += 1
                    output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
                    batch_chunk.to_parquet(output_file, index=False)
                    total_processed += len(batch_chunk)
                    print(f"保存批次 {batch_count}: {len(batch_chunk)} 条记录")

                    start_idx = end_idx
            else:
                # 文件不大，添加到累积数据
                batch_data.append(df)
                current_size += len(df)

                # 检查是否达到批次大小
                if current_size >= batch_size:
                    # 合并数据
                    merged_df = pd.concat(batch_data, ignore_index=True)

                    # 保存批次
                    batch_count += 1
                    output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")

                    if len(merged_df) > batch_size:
                        # 分割数据
                        merged_df.iloc[:batch_size].to_parquet(output_file, index=False)
                        batch_data = [merged_df.iloc[batch_size:]]
                        current_size = len(merged_df) - batch_size
                        total_processed += batch_size
                    else:
                        # 保存全部数据
                        merged_df.to_parquet(output_file, index=False)
                        batch_data = []
                        current_size = 0
                        total_processed += len(merged_df)

                    print(f"保存批次 {batch_count}: {min(batch_size, len(merged_df))} 条记录")
                    
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue
    
    # 保存最后一批数据
    if batch_data and current_size > 0:
        df = pd.concat(batch_data, ignore_index=True)
        batch_count += 1
        output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
        df.to_parquet(output_file, index=False)
        total_processed += len(df)
        print(f"保存最终批次 {batch_count}: {len(df)} 条记录")
    
    print(f"\n处理完成！")
    print(f"总记录数: {total_processed:,}")
    print(f"批次文件数: {batch_count}")
    print(f"输出目录: {output_dir}")

if __name__ == "__main__":
    main()
